<!-- templates/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HR Expert Assistant</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
  <div class="container">
    <header>
      <h1>🧠 Human Resources Expert Assistant</h1>
      <p>Your intelligent HR consultant for all resource planning and talent management needs.</p>
      <p>Supports both English and French! (Prend en charge l'anglais et le français!)</p>
    </header>

    <div class="api-section">
      <h2>Configuration</h2>
      <p>Enter your Groq API key to get started</p>
      <div class="api-input">
        <input type="password" id="api-key-input" placeholder="Enter your Groq API key here...">
        <button id="configure-api-btn">Configure API Key</button>
      </div>
      <div class="api-options">
        <label class="checkbox-container">
          <input type="checkbox" id="use-cached-data" checked>
          <span class="checkmark"></span>
          Use cached data (faster startup, avoids re-scraping websites)
        </label>
        <div class="api-info">
          <p>The assistant will use information from:</p>
          <ul>
            <li>HR-related websites (scraped automatically)</li>
            <li>PDF files in the 'data' directory (processed automatically)</li>
          </ul>
        </div>
      </div>
      <div class="api-status" id="api-status">Please configure your API key to start chatting</div>
    </div>

    <div class="chat-container">
      <div class="chat-messages" id="chat-messages">
        <div class="message bot-message">
          Welcome to the HR Expert Assistant! Please configure your API key to start chatting.
        </div>
      </div>
      <div class="chat-input">
        <input type="text" id="user-input" placeholder="Ask in English or French: How can I assess the skills needed for a new project?" disabled>
        <button id="send-btn" disabled>Send</button>
      </div>
      <div class="chat-buttons">
        <button id="clear-btn">Clear Chat / Effacer la conversation</button>
      </div>
    </div>

    <div class="examples-section">
      <div class="examples-container">
        <h3>Example questions (English):</h3>
        <div class="examples-grid" id="english-examples">
          <div class="example-item">How do I create a skills matrix for my team?</div>
          <div class="example-item">What's the best approach to resource planning for a 6-month project?</div>
          <div class="example-item">How can I improve employee retention in my IT department?</div>
          <div class="example-item">What training programs should I implement for improving leadership skills?</div>
          <div class="example-item">How do I identify skill gaps in my team for an upcoming project?</div>
          <div class="example-item">What are the best practices for performance evaluation?</div>
        </div>

        <h3>Exemples de questions (Français):</h3>
        <div class="examples-grid" id="french-examples">
          <div class="example-item">Comment créer une matrice de compétences pour mon équipe?</div>
          <div class="example-item">Quelle est la meilleure approche pour la planification des ressources d'un projet de 6 mois?</div>
          <div class="example-item">Comment améliorer la rétention des employés dans mon département informatique?</div>
          <div class="example-item">Quels programmes de formation devrais-je mettre en œuvre pour améliorer les compétences en leadership?</div>
          <div class="example-item">Comment identifier les lacunes en compétences dans mon équipe pour un projet à venir?</div>
          <div class="example-item">Quelles sont les meilleures pratiques pour l'évaluation des performances?</div>
        </div>
      </div>
    </div>
  </div>

  <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>