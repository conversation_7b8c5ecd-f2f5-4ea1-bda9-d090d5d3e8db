<!-- templates/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HR Expert Assistant - Login</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/login.css') }}">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <div class="logo-icon">🧠</div>
          <h1>HR Expert Assistant</h1>
        </div>
        <p class="subtitle">Your intelligent HR consultant for resource planning and talent management</p>
      </div>

      <form class="login-form" id="login-form">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" placeholder="Enter your username" required>
        </div>

        <div class="form-group">
          <label for="api-key">Groq API Key</label>
          <input type="password" id="api-key" name="api_key" placeholder="Enter your Groq API key" required>
          <small class="help-text">Get your API key from <a href="https://console.groq.com/" target="_blank">console.groq.com</a></small>
        </div>

        <div class="form-group checkbox-group">
          <label class="checkbox-container">
            <input type="checkbox" id="use-cached-data" checked>
            <span class="checkmark"></span>
            Use cached data (faster startup)
          </label>
        </div>

        <button type="submit" class="login-btn" id="login-btn">
          <span class="btn-text">Start Chat Session</span>
          <div class="btn-loader" style="display: none;">
            <div class="spinner"></div>
          </div>
        </button>

        <div class="status-message" id="status-message"></div>
      </form>

      <div class="features">
        <h3>Features</h3>
        <ul>
          <li>🌐 Bilingual support (English & French)</li>
          <li>📊 HR data from multiple sources</li>
          <li>📄 PDF document processing</li>
          <li>🤖 AI-powered responses</li>
        </ul>
      </div>
    </div>
  </div>

  <script src="{{ url_for('static', filename='js/login.js') }}"></script>
</body>
</html>