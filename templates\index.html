<!-- templates/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HR Expert Assistant - Login</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/login.css') }}">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <div class="logo-icon">🧠</div>
          <h1>HR Expert Assistant</h1>
        </div>
        <p class="subtitle">Your intelligent HR consultant for resource planning and talent management</p>
      </div>

      <form class="login-form" id="login-form">
        <div class="form-group">
          <label for="username">اسم المستخدم / Username</label>
          <input type="text" id="username" name="username" placeholder="Enter your username" required>
        </div>

        <div class="form-group">
          <label for="api-key">Groq API Key</label>
          <input type="password" id="api-key" name="api_key" placeholder="Enter your Groq API key" required>
          <small class="help-text">Get your API key from <a href="https://console.groq.com/" target="_blank">console.groq.com</a></small>
        </div>

        <div class="form-group checkbox-group">
          <label class="checkbox-container">
            <input type="checkbox" id="use-cached-data" checked>
            <span class="checkmark"></span>
            Use cached data (faster startup)
          </label>
        </div>

        <button type="submit" class="login-btn" id="login-btn">
          <span class="btn-text">Start Chat Session</span>
          <div class="btn-loader" style="display: none;">
            <div class="spinner"></div>
          </div>
        </button>

        <div class="status-message" id="status-message"></div>
      </form>

      <div class="features">
        <h3>Features / الميزات</h3>
        <ul>
          <li>🌐 Bilingual support (English & French)</li>
          <li>📊 HR data from multiple sources</li>
          <li>📄 PDF document processing</li>
          <li>🤖 AI-powered responses</li>
        </ul>
      </div>
    </div>
  </div>

  <script src="{{ url_for('static', filename='js/login.js') }}"></script>
</body>
</html>

    <div class="chat-container">
      <div class="chat-messages" id="chat-messages">
        <div class="message bot-message">
          Welcome to the HR Expert Assistant! Please configure your API key to start chatting.
        </div>
      </div>
      <div class="chat-input">
        <input type="text" id="user-input" placeholder="Ask in English or French: How can I assess the skills needed for a new project?" disabled>
        <button id="send-btn" disabled>Send</button>
      </div>
      <div class="chat-buttons">
        <button id="clear-btn">Clear Chat / Effacer la conversation</button>
      </div>
    </div>

    <div class="examples-section">
      <div class="examples-container">
        <h3>Example questions (English):</h3>
        <div class="examples-grid" id="english-examples">
          <div class="example-item">How do I create a skills matrix for my team?</div>
          <div class="example-item">What's the best approach to resource planning for a 6-month project?</div>
          <div class="example-item">How can I improve employee retention in my IT department?</div>
          <div class="example-item">What training programs should I implement for improving leadership skills?</div>
          <div class="example-item">How do I identify skill gaps in my team for an upcoming project?</div>
          <div class="example-item">What are the best practices for performance evaluation?</div>
        </div>

        <h3>Exemples de questions (Français):</h3>
        <div class="examples-grid" id="french-examples">
          <div class="example-item">Comment créer une matrice de compétences pour mon équipe?</div>
          <div class="example-item">Quelle est la meilleure approche pour la planification des ressources d'un projet de 6 mois?</div>
          <div class="example-item">Comment améliorer la rétention des employés dans mon département informatique?</div>
          <div class="example-item">Quels programmes de formation devrais-je mettre en œuvre pour améliorer les compétences en leadership?</div>
          <div class="example-item">Comment identifier les lacunes en compétences dans mon équipe pour un projet à venir?</div>
          <div class="example-item">Quelles sont les meilleures pratiques pour l'évaluation des performances?</div>
        </div>
      </div>
    </div>
  </div>

  <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>