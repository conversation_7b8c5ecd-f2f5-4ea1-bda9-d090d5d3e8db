# app.py
from flask import Flask, render_template, request, jsonify
from hr_assistant import HRAssistant
import os

app = Flask(__name__)
hr_assistant = None

@app.route('/')
def index():
    """Render the main page"""
    return render_template('index.html')

@app.route('/api/configure', methods=['POST'])
def configure_api():
    """Configure the API key and initialize HR assistant"""
    global hr_assistant

    data = request.json
    api_key = data.get('api_key')
    use_cached_data = data.get('use_cached_data', True)  # Default to True

    if not api_key:
        return jsonify({"success": False, "message": "API key is required"}), 400

    try:
        # Initialize the HR assistant with the API key
        hr_assistant = HRAssistant(api_key, use_cached_data=use_cached_data)
        return jsonify({"success": True, "message": "API key configured successfully"}), 200
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error configuring API key: {str(e)}\n{error_details}")
        return jsonify({"success": False, "message": f"Error configuring API key. Please check your API key and try again."}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    """Process chat messages"""
    global hr_assistant

    if not hr_assistant:
        return jsonify({"success": False, "message": "Please configure API key first"}), 400

    data = request.json
    message = data.get('message')
    history = data.get('history', [])

    if not message:
        return jsonify({"success": False, "message": "Message is required"}), 400

    try:
        # Process the message
        response = hr_assistant.process_message(message, history)
        return jsonify({"success": True, "response": response}), 200
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error processing message: {str(e)}\n{error_details}")

        # Provide a fallback response
        fallback_response = "I'm sorry, I encountered an error processing your request. "
        fallback_response += "As an HR assistant, I can still try to help with general HR questions. "
        fallback_response += "Could you try rephrasing your question or asking something else about HR management?"

        return jsonify({"success": True, "response": fallback_response}), 200

if __name__ == '__main__':
    app.run(debug=True)