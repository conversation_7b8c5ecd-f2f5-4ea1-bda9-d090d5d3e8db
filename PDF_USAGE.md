# Using PDF Files with HR Expert Assistant

The HR Expert Assistant now supports extracting information from PDF files in addition to scraping HR-related websites. This allows you to provide domain-specific knowledge to the assistant through your own PDF documents.

## How to Use PDF Files

1. **Add PDF Files**: 
   - Place your HR-related PDF files in the `data` directory
   - The assistant will automatically process these files during initialization

2. **Supported Content**:
   - HR policies and procedures
   - Employee handbooks
   - Training materials
   - HR research papers
   - Any other HR-related documents

3. **File Processing**:
   - PDF files are processed automatically when the assistant starts
   - Text is extracted from each page of the PDF
   - The extracted text is combined with web-scraped data
   - All data is used to answer HR-related questions

## Tips for Best Results

1. **Use Clear, Text-Based PDFs**:
   - The assistant works best with PDFs that contain actual text (not scanned images)
   - PDFs with clear formatting and structure will yield better results

2. **Organize Your PDFs**:
   - Use descriptive filenames for your PDFs
   - The filename will be used as the title in the knowledge base

3. **Content Focus**:
   - Focus on adding PDFs with HR-specific content
   - More specific content will lead to more accurate answers

## Testing PDF Processing

You can test the PDF processing functionality by running:

```
python test_pdf_processing.py
```

This script will:
1. Check for PDF files in the `data` directory
2. Process each PDF file
3. Display information about the extracted content

## Troubleshooting

If you encounter issues with PDF processing:

1. **Check File Format**:
   - Ensure your PDFs are text-based and not scanned images
   - Try converting problematic PDFs to text format

2. **File Size**:
   - Very large PDFs may take longer to process
   - Consider splitting large PDFs into smaller documents

3. **Encoding Issues**:
   - If your PDFs contain special characters, ensure they use UTF-8 encoding

4. **Debug Information**:
   - Check the console output for any error messages during PDF processing
