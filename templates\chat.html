<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HR Expert Assistant - Chat</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/chat.css') }}">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
  <div class="chat-app">
    <!-- Header -->
    <header class="chat-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">🧠</div>
          <div class="logo-text">
            <h1>HR Expert Assistant</h1>
            <span class="status-indicator" id="status-indicator">Ready</span>
          </div>
        </div>
        <div class="user-section">
          <span class="welcome-text">Welcome, <span id="username-display">User</span></span>
          <button class="logout-btn" id="logout-btn">Logout</button>
        </div>
      </div>
    </header>

    <!-- Main Chat Area -->
    <main class="chat-main">
      <div class="chat-container">
        <div class="chat-messages" id="chat-messages">
          <div class="welcome-message">
            <div class="welcome-icon">👋</div>
            <h2>Welcome to HR Expert Assistant!</h2>
            <p>I'm here to help you with HR-related questions, resource planning, talent management, and more.</p>
            <p>You can ask me questions in both English and French.</p>
          </div>
        </div>

        <div class="chat-input-container">
          <div class="chat-input-wrapper">
            <textarea 
              id="user-input" 
              placeholder="Ask me anything about HR, talent management, or resource planning..."
              rows="1"
            ></textarea>
            <button id="send-btn" class="send-btn" disabled>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22,2 15,22 11,13 2,9"></polygon>
              </svg>
            </button>
          </div>
          <div class="input-actions">
            <button id="clear-btn" class="action-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
              </svg>
              Clear Chat
            </button>
          </div>
        </div>
      </div>

      <!-- Sidebar with examples -->
      <aside class="chat-sidebar">
        <div class="sidebar-content">
          <h3>Example Questions</h3>
          
          <div class="examples-section">
            <h4>English Examples</h4>
            <div class="example-questions">
              <button class="example-btn" data-question="How do I create a skills matrix for my team?">
                How do I create a skills matrix for my team?
              </button>
              <button class="example-btn" data-question="What's the best approach to resource planning for a 6-month project?">
                What's the best approach to resource planning?
              </button>
              <button class="example-btn" data-question="How can I improve employee retention in my IT department?">
                How can I improve employee retention?
              </button>
              <button class="example-btn" data-question="What training programs should I implement for leadership skills?">
                What training programs for leadership?
              </button>
            </div>
          </div>

          <div class="examples-section">
            <h4>French Examples</h4>
            <div class="example-questions">
              <button class="example-btn" data-question="Comment créer une matrice de compétences pour mon équipe?">
                Comment créer une matrice de compétences?
              </button>
              <button class="example-btn" data-question="Quelle est la meilleure approche pour la planification des ressources?">
                Planification des ressources?
              </button>
              <button class="example-btn" data-question="Comment améliorer la rétention des employés?">
                Améliorer la rétention des employés?
              </button>
              <button class="example-btn" data-question="Quels programmes de formation pour le leadership?">
                Programmes de formation leadership?
              </button>
            </div>
          </div>
        </div>
      </aside>
    </main>
  </div>

  <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
</body>
</html>
