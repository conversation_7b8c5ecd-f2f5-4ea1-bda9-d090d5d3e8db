"""
Test script for PDF processing functionality.
This script will test the PDF processing functionality of the HR Assistant.
"""
import os
from hr_assistant import HRAssistant

def main():
    print("Testing PDF processing functionality...")
    
    # Create data directory if it doesn't exist
    data_dir = "data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"Created directory: {data_dir}")
    
    # Check if there are any PDF files in the data directory
    pdf_files = [f for f in os.listdir(data_dir) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("No PDF files found in the data directory.")
        print("Please add some PDF files to the data directory and run this script again.")
        return
    
    print(f"Found {len(pdf_files)} PDF files in the data directory:")
    for pdf_file in pdf_files:
        print(f"  - {pdf_file}")
    
    # Initialize HR Assistant with a dummy API key (won't be used for this test)
    dummy_api_key = "dummy_api_key"
    
    # Create a custom HR Assistant that only processes PDFs
    class TestHRAssistant(HRAssistant):
        def __init__(self):
            # Skip the parent class initialization
            self.api_key = dummy_api_key
            
        def test_pdf_processing(self):
            return self.process_pdf_files()
    
    # Create an instance of the test assistant
    test_assistant = TestHRAssistant()
    
    # Process PDF files
    pdf_data = test_assistant.test_pdf_processing()
    
    if not pdf_data:
        print("No data extracted from PDF files.")
        return
    
    print(f"\nSuccessfully extracted data from {len(pdf_data)} PDF files:")
    for i, data in enumerate(pdf_data):
        print(f"\nPDF {i+1}: {data['title']}")
        print(f"Source: {data['source']}")
        print(f"Content length: {len(data['content'])} characters")
        print(f"Content preview: {data['content'][:200]}...")

if __name__ == "__main__":
    main()
