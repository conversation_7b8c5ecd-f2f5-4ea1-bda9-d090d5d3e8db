# hr_assistant.py
from langchain.schema import HumanMessage
from langchain.memory import ConversationBufferMemory
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_groq import ChatGroq
from langchain_core.vectorstores import InMemoryVectorStore
from bs4 import BeautifulSoup
import requests
import re
import os
import json
import datetime
import glob
from pypdf import Pdf<PERSON>eader
from typing import List, Dict, Any

class HRAssistant:
    """HR Assistant class that handles processing HR-related queries"""

    def __init__(self, api_key, use_cached_data=True):
        """Initialize the HR Assistant with API key"""
        self.api_key = api_key
        os.environ['GROQ_API_KEY'] = api_key

        # Initialize components
        self.initialize_components()

        try:
            # Try to load cached data first if use_cached_data is True
            self.hr_data = []
            if use_cached_data:
                self.hr_data = self.load_hr_data()

            # If no cached data or use_cached_data is False, scrape new data
            if not self.hr_data:
                self.hr_data = self.scrape_hr_data()

            # Prepare documents from the data
            self.chunks = self.prepare_hr_documents(self.hr_data)

            # Initialize embeddings and vector store
            self.initialize_retriever()
        except Exception as e:
            print(f"Warning: Error during initialization: {str(e)}")
            # Create empty placeholders if initialization fails
            self.hr_data = []
            self.chunks = ["HR management involves organizing, directing, and developing employees to effectively meet organizational goals."]
            self.retriever = None

    def load_hr_data(self):
        """Load HR data from the latest saved file"""
        data_dir = "data"
        file_path = os.path.join(data_dir, "hr_scraped_data_latest.json")

        hr_data = []

        # Load scraped web data if available
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    hr_data = json.load(f)
                print(f"Loaded {len(hr_data)} items from {file_path}")
            except Exception as e:
                print(f"Error loading HR data from file: {str(e)}")
        else:
            print(f"No cached data found at {file_path}")

        # Load PDF data
        pdf_data = self.process_pdf_files()
        if pdf_data:
            hr_data.extend(pdf_data)
            print(f"Added {len(pdf_data)} items from PDF files")

        return hr_data

    def process_pdf_files(self):
        """Process PDF files in the data directory"""
        data_dir = "data"
        pdf_files = glob.glob(os.path.join(data_dir, "*.pdf"))

        if not pdf_files:
            print("No PDF files found in the data directory")
            return []

        pdf_data = []

        for pdf_file in pdf_files:
            try:
                print(f"Processing PDF file: {pdf_file}")

                # Extract text from PDF
                pdf_text = self.extract_text_from_pdf(pdf_file)

                if not pdf_text.strip():
                    print(f"No text extracted from {pdf_file}")
                    continue

                # Create data item
                data = {
                    'title': os.path.basename(pdf_file),
                    'content': pdf_text,
                    'source': f"PDF file: {os.path.basename(pdf_file)}",
                    'processed_at': datetime.datetime.now().isoformat()
                }

                pdf_data.append(data)
                print(f"Successfully processed {pdf_file}")

            except Exception as e:
                print(f"Error processing PDF file {pdf_file}: {str(e)}")

        return pdf_data

    def extract_text_from_pdf(self, pdf_path):
        """Extract text from a PDF file"""
        try:
            reader = PdfReader(pdf_path)
            text = ""

            for page in reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n\n"

            # Clean up the text
            text = re.sub(r'\s+', ' ', text)  # Replace multiple spaces with a single space
            text = text.strip()

            return text
        except Exception as e:
            print(f"Error extracting text from PDF {pdf_path}: {str(e)}")
            return ""

    def initialize_components(self):
        """Initialize LLM and memory components"""
        try:
            # Initialize LLM with Groq
            self.llm = ChatGroq(
                temperature=0.7,
                model_name="meta-llama/llama-4-scout-17b-16e-instruct",
                default_headers={"Content-Type": "application/json; charset=utf-8"},
                max_tokens=2048,  # Set maximum tokens to avoid long responses
                timeout=60  # Set timeout to 60 seconds
            )

            # Initialize conversation memory
            self.memory = ConversationBufferMemory(
                memory_key="history",
                return_messages=True,
                output_key="output"
            )
        except Exception as e:
            raise Exception(f"Failed to initialize components: {str(e)}")

    def initialize_retriever(self):
        """Initialize embeddings and retriever"""
        try:
            # Use a simpler approach for embeddings to avoid errors
            try:
                # Try to import from the new location first
                from langchain_community.embeddings import HuggingFaceEmbeddings as CommunityHuggingFaceEmbeddings
                self.embeddings = CommunityHuggingFaceEmbeddings(
                    model_name="all-MiniLM-L6-v2",
                    model_kwargs={'device': 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )
            except ImportError:
                # Fall back to the old location
                self.embeddings = HuggingFaceEmbeddings(
                    model_name="all-MiniLM-L6-v2",
                    model_kwargs={'device': 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )

            # Make sure we have some chunks to work with
            if not self.chunks or len(self.chunks) == 0:
                self.chunks = ["HR management involves organizing, directing, and developing employees to effectively meet organizational goals."]

            # Initialize vector store
            self.vectorstore = InMemoryVectorStore.from_texts(
                texts=self.chunks,
                embedding=self.embeddings,
            )

            # Initialize retriever
            self.retriever = self.vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": 5}
            )
        except Exception as e:
            raise Exception(f"Failed to initialize retriever: {str(e)}")

    def scrape_hr_data(self) -> List[Dict]:
        """Scrape data about human resources, competencies, and talent management"""
        # Create data directory if it doesn't exist
        data_dir = "data"
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"Created directory: {data_dir}")

        urls = [
            'https://www.shrm.org/',  # Society for Human Resource Management
            'https://www.cipd.org/en/',  # Chartered Institute of Personnel and Development
            'https://www.hrci.org/',  # HR Certification Institute
            'https://www.hrbartender.com/',  # HR Bartender blog
            'https://www.humanresourcestoday.com/',  # Human Resources Today
            'https://www.aihr.com/blog/',  # Academy to Innovate HR
            'https://www.hrdive.com/',  # HR Dive
            'https://www.hrzone.com/',  # HR Zone
            'https://www.talentlyft.com/en/resources/what-is-talent-management',  # Talent Management
            'https://fr.indeed.com/recrutement/outils-de-recrutement?hl=fr&from=gnav-employer--allspark--employer&co=FR',  # Skills Assessment
        ]

        hr_data = []
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        for url in urls:
            try:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                soup = BeautifulSoup(response.content, 'html.parser')

                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()

                # Extract main content based on common content containers
                content_selectors = ['article', 'main', '.content', '#content', '.post-content', '.entry-content', '.article-body']
                content = None
                for selector in content_selectors:
                    content = soup.select_one(selector)
                    if content:
                        break

                if not content:
                    content = soup.body  # Fallback to body if no specific content container found

                # Extract text content
                text = ' '.join(content.stripped_strings)

                # Try to find title
                title = ''
                title_tag = soup.find('h1') or soup.find('title')
                if title_tag:
                    title = title_tag.get_text().strip()

                data = {
                    'title': title,
                    'content': text,
                    'source': url,
                    'scraped_at': datetime.datetime.now().isoformat()
                }
                hr_data.append(data)
                print(f"Successfully scraped {url}")

            except Exception as e:
                print(f"Error scraping {url}: {str(e)}")

        # Process PDF files
        pdf_data = self.process_pdf_files()
        if pdf_data:
            hr_data.extend(pdf_data)
            print(f"Added {len(pdf_data)} items from PDF files")

        # Save the combined data to a JSON file
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = os.path.join(data_dir, f"hr_scraped_data_{timestamp}.json")

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(hr_data, f, ensure_ascii=False, indent=4)

            print(f"Saved combined data to {file_path}")

            # Also save to a fixed filename for easy access
            fixed_file_path = os.path.join(data_dir, "hr_scraped_data_latest.json")
            with open(fixed_file_path, 'w', encoding='utf-8') as f:
                json.dump(hr_data, f, ensure_ascii=False, indent=4)

            print(f"Saved combined data to {fixed_file_path}")
        except Exception as e:
            print(f"Error saving data to file: {str(e)}")

        return hr_data

    def prepare_hr_documents(self, hr_data: List[Dict]):
        """Prepare and clean scraped HR data for processing"""
        documents = []
        for item in hr_data:
            # Clean and normalize text
            content = item['content']
            # Remove extra whitespace and normalize line endings
            content = ' '.join(content.split())

            # Create structured document
            text = f"""
Source: {item['source']}
Title: {item['title']}
Content: {content}
"""
            documents.append(text)

        # Split into smaller chunks for better processing
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )

        chunks = text_splitter.split_text('\n\n'.join(documents))

        # Remove any chunks that are too short
        chunks = [chunk for chunk in chunks if len(chunk.strip()) > 100]

        return chunks

    def detect_language(self, text):
        """Detect if the text is in English or French"""
        # Common French words and patterns
        french_patterns = [
            r'\b(je|tu|il|elle|nous|vous|ils|elles)\b',
            r'\b(suis|es|est|sommes|êtes|sont)\b',
            r'\b(le|la|les|un|une|des|du|de la)\b',
            r'\b(et|ou|mais|donc|car|quand|comment|pourquoi)\b',
            r'\b(bonjour|salut|merci|s\'il vous plaît|au revoir)\b',
            r'\b(ressources humaines|compétences|formation|recrutement|personnel)\b'
        ]

        # Count matches for French patterns
        french_count = 0
        for pattern in french_patterns:
            if re.search(pattern, text.lower()):
                french_count += 1

        # If multiple French patterns are found, likely French
        if french_count >= 2:
            return 'french'

        # Default to English for simplicity
        return 'english'

    def is_greeting(self, message, language):
        """Check if a message is a greeting"""
        english_greeting_keywords = ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"]
        french_greeting_keywords = ["bonjour", "salut", "bonsoir", "coucou", "allo", "bienvenue"]

        if language == 'english' and any(greeting.lower() in message.lower() for greeting in english_greeting_keywords):
            return True
        elif language == 'french' and any(greeting.lower() in message.lower() for greeting in french_greeting_keywords):
            return True

        return False

    def is_hr_related(self, query):
        """Determine if a query is related to HR topics"""
        # English HR keywords
        english_hr_keywords = [
            "hr", "human resource", "staff", "employee", "training", "skill", "competency",
            "hiring", "recruit", "talent", "personnel", "workforce", "career", "job",
            "performance", "evaluation", "assessment", "development", "learning", "retention",
            "compensation", "salary", "benefit", "onboarding", "offboarding", "team",
            "leadership", "management", "culture", "diversity", "inclusion", "engagement",
            "motivation", "conflict", "resolution", "succession", "planning", "resource"
        ]

        # French HR keywords
        french_hr_keywords = [
            "rh", "ressources humaines", "personnel", "employé", "formation", "compétence",
            "embauche", "recrutement", "talent", "carrière", "emploi", "performance",
            "évaluation", "développement", "apprentissage", "rétention", "compensation",
            "salaire", "avantage", "intégration", "équipe", "leadership", "gestion",
            "culture", "diversité", "inclusion", "engagement", "motivation", "conflit",
            "résolution", "succession", "planification", "ressource"
        ]

        # Combine all keywords
        all_hr_keywords = english_hr_keywords + french_hr_keywords

        # Check if any HR keyword is in the query
        return any(keyword.lower() in query.lower() for keyword in all_hr_keywords)

    def retrieve_context(self, query):
        """Retrieve relevant context for the question"""
        try:
            # Check if retriever is available
            if self.retriever is None:
                return ""

            # Try to get relevant documents
            docs = self.retriever.get_relevant_documents(query)
            context = "\n\n".join([f"- {doc.page_content}" for doc in docs])
            return context
        except Exception as e:
            print(f"Error retrieving context: {str(e)}")
            # Return empty context if retrieval fails
            return ""

    def process_message(self, user_message, history=[]):
        """Process user message and generate response"""
        try:
            # Load conversation history into memory
            self.memory.clear()
            for item in history:
                self.memory.save_context(
                    {"input": item['input']},
                    {"output": item['output']}
                )

            # Detect language
            language = self.detect_language(user_message)

            # Check for greetings
            if self.is_greeting(user_message, language):
                if language == 'french':
                    greeting_response = "Bonjour ! Je suis votre Assistant Expert en Ressources Humaines. Je peux vous aider avec la planification des ressources, le développement des compétences, l'acquisition de talents, les programmes de formation, et plus encore. Comment puis-je vous aider aujourd'hui ?"
                else:
                    greeting_response = "Hello! I'm your HR Expert Assistant. I can help you with resource planning, competency development, talent acquisition, training programs, and more. How can I assist you today?"

                return greeting_response

            # Check if query is HR-related
            if not self.is_hr_related(user_message):
                if language == 'french':
                    off_topic_response = "Je suis spécialisé dans la gestion des ressources humaines, le développement des compétences et la planification des ressources. Je n'ai pas d'informations spécifiques sur ce sujet. Pourriez-vous me poser une question liée aux RH, comme la formation du personnel, l'évaluation des compétences, la planification des ressources ou la gestion des talents ?"
                else:
                    off_topic_response = "I'm specialized in human resources management, competency development, and resource planning. I don't have specific information about that topic. Could you ask me something related to HR, such as staff training, competency assessment, resource planning, or talent management?"

                return off_topic_response

            # Get context
            context = self.retrieve_context(user_message)

            # Check if we have relevant context
            is_relevant = len(context) > 200 and not context.startswith("Error retrieving context")

            # Create prompt with HR focus in the appropriate language
            if language == 'french':
                prompt = f"""En vous basant sur le contexte suivant concernant la gestion des ressources humaines:

Contexte:
{context}

Question:
{user_message}

Suivez ces instructions attentivement:
1. Si la question concerne les ressources humaines, le développement des compétences, la planification des ressources, la gestion des talents ou des sujets RH connexes, fournissez une réponse détaillée.
2. S'il s'agit d'une salutation, répondez chaleureusement et présentez vos capacités.
3. Si la question est liée aux RH mais que vous n'avez pas d'informations spécifiques, reconnaissez-le et fournissez plutôt des meilleures pratiques générales au lieu d'inventer des informations.

Pour les questions liées aux RH, incluez:
1. Les meilleures pratiques en gestion des ressources humaines
2. Les stratégies de développement des compétences
3. Les recommandations de planification des ressources
4. Les approches d'acquisition et de rétention des talents
5. Les solutions de formation et de développement
6. Les considérations de gestion de la performance

Soyez conversationnel, utile et concentré sur l'expertise RH. Si vous n'avez pas d'informations spécifiques, soyez honnête à ce sujet plutôt que d'inventer des détails.

IMPORTANT: Répondez en français car l'utilisateur a posé sa question en français."""
            else:  # Default to English
                prompt = f"""Based on the following context about human resources management:

Context:
{context}

Question:
{user_message}

Follow these instructions carefully:
1. If the question is about human resources, competency development, resource planning, talent management, or related HR topics, provide a detailed response.
2. If greeting, respond warmly and introduce your capabilities.
3. If the question is HR-related but you don't have specific information, acknowledge this and provide general best practices instead of making up information.

For HR-related questions, include:
1. Best practices in human resources management
2. Competency development strategies
3. Resource planning recommendations
4. Talent acquisition and retention approaches
5. Training and development solutions
6. Performance management considerations

Remember to be conversational, helpful, and focused on HR expertise. If you don't have specific information, be honest about it rather than making up details.

IMPORTANT: Respond in English as the user asked their question in English."""

            # Generate response
            response = self.llm([HumanMessage(content=prompt)]).content

            # If context wasn't relevant, provide a more general HR response
            if not is_relevant:
                if language == 'french':
                    general_hr_prompt = f"""Vous êtes un expert en gestion des ressources humaines.
L'utilisateur a demandé: "{user_message}"

Cette question est liée aux RH, mais vous n'avez pas de données spécifiques sur ce sujet exact.
Fournissez une réponse utile basée sur les meilleures pratiques générales en RH.
Soyez honnête sur le fait que vous n'avez pas de données spécifiques sur ce sujet exact, mais offrez des conseils généraux précieux.
Concentrez-vous sur l'aide tout en étant clair sur les limites de vos connaissances.

IMPORTANT: Répondez en français car l'utilisateur a posé sa question en français."""
                else:  # Default to English
                    general_hr_prompt = f"""You are an expert in Human Resources management.
The user has asked: "{user_message}"

This question is related to HR, but you don't have specific data on this exact topic.
Provide a helpful response based on general HR best practices and knowledge.
Be honest that you don't have specific data on this exact topic, but offer valuable general guidance.
Focus on being helpful while being clear about the limitations of your knowledge.

IMPORTANT: Respond in English as the user asked their question in English."""

                general_response = self.llm([HumanMessage(content=general_hr_prompt)]).content
                response = general_response

            return response

        except Exception as e:
            error_message = f"An error occurred: {str(e)}"
            return error_message