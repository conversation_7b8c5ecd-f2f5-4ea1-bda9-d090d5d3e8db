// Login page functionality
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginBtn = document.getElementById('login-btn');
    const btnText = document.querySelector('.btn-text');
    const btnLoader = document.querySelector('.btn-loader');
    const statusMessage = document.getElementById('status-message');

    loginForm.addEventListener('submit', handleLogin);

    async function handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const apiKey = document.getElementById('api-key').value.trim();
        const useCachedData = document.getElementById('use-cached-data').checked;

        // Validation
        if (!username) {
            showMessage('Please enter a username', 'error');
            return;
        }

        if (!apiKey) {
            showMessage('Please enter your Groq API key', 'error');
            return;
        }

        // Show loading state
        setLoadingState(true);
        hideMessage();

        try {
            // Configure API
            const response = await fetch('/api/configure', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    api_key: apiKey,
                    use_cached_data: useCachedData
                })
            });

            const data = await response.json();

            if (data.success) {
                showMessage('Configuration successful! Redirecting...', 'success');
                
                // Store username in sessionStorage
                sessionStorage.setItem('username', username);
                
                // Redirect to chat page after a short delay
                setTimeout(() => {
                    window.location.href = '/chat';
                }, 1500);
            } else {
                showMessage(data.message || 'Configuration failed. Please check your API key.', 'error');
                setLoadingState(false);
            }
        } catch (error) {
            console.error('Login error:', error);
            showMessage('Connection error. Please try again.', 'error');
            setLoadingState(false);
        }
    }

    function setLoadingState(loading) {
        loginBtn.disabled = loading;
        if (loading) {
            btnText.style.display = 'none';
            btnLoader.style.display = 'flex';
        } else {
            btnText.style.display = 'block';
            btnLoader.style.display = 'none';
        }
    }

    function showMessage(message, type) {
        statusMessage.textContent = message;
        statusMessage.className = `status-message ${type}`;
        statusMessage.style.display = 'block';
    }

    function hideMessage() {
        statusMessage.style.display = 'none';
    }

    // Auto-focus on username field
    document.getElementById('username').focus();
});
