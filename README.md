# HR Expert Assistant

A bilingual (English/French) HR Expert Assistant that helps with resource planning, competency development, talent acquisition, training programs, and more.

## Features

- Interactive chat interface with a modern design
- Bilingual support (English and French)
- Automatic language detection
- HR-specific knowledge and expertise
- Example questions for quick reference
- Powered by Groq LLM API

## Requirements

- Python 3.8+
- Groq API key (https://console.groq.com/)
- Internet connection for API calls and data retrieval

## Installation

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/hr-expert-assistant.git
   cd hr-expert-assistant
   ```

2. Create a virtual environment (recommended):
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows:
     ```
     venv\Scripts\activate
     ```
   - macOS/Linux:
     ```
     source venv/bin/activate
     ```

4. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

## Usage

1. Start the application:
   ```
   python app.py
   ```

2. Open your web browser and navigate to:
   ```
   http://127.0.0.1:5000/
   ```

3. Enter your Groq API key in the configuration section

4. Start chatting with the HR Expert Assistant!

## Example Questions

### English
- How do I create a skills matrix for my team?
- What's the best approach to resource planning for a 6-month project?
- How can I improve employee retention in my IT department?
- What training programs should I implement for improving leadership skills?

### French
- Comment créer une matrice de compétences pour mon équipe?
- Quelle est la meilleure approche pour la planification des ressources d'un projet de 6 mois?
- Comment améliorer la rétention des employés dans mon département informatique?
- Quels programmes de formation devrais-je mettre en œuvre pour améliorer les compétences en leadership?

## Project Structure

- `app.py` - Main Flask application
- `hr_assistant.py` - HR Assistant logic and API integration
- `templates/index.html` - HTML template for the web interface
- `static/css/style.css` - CSS styles for the web interface
- `static/js/script.js` - JavaScript for the web interface

## License

MIT
