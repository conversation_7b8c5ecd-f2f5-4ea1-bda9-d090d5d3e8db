/* static/js/script.js */

// Main variables
let apiKey = null;
let chatHistory = [];
let conversationMemory = [];
let isApiConfigured = false;

// DOM Elements
const apiKeyInput = document.getElementById('api-key-input');
const configureApiBtn = document.getElementById('configure-api-btn');
const apiStatus = document.getElementById('api-status');
const chatMessages = document.getElementById('chat-messages');
const userInput = document.getElementById('user-input');
const sendBtn = document.getElementById('send-btn');
const clearBtn = document.getElementById('clear-btn');
const englishExamples = document.getElementById('english-examples');
const frenchExamples = document.getElementById('french-examples');
const useCachedDataCheckbox = document.getElementById('use-cached-data');

// Initialize event listeners
function initEventListeners() {
  // Configure API key
  configureApiBtn.addEventListener('click', configureApiKey);

  // Send message
  sendBtn.addEventListener('click', sendMessage);
  userInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  });

  // Clear chat
  clearBtn.addEventListener('click', clearChat);

  // Example questions
  const allExampleItems = document.querySelectorAll('.example-item');
  allExampleItems.forEach(item => {
    item.addEventListener('click', () => {
      if (isApiConfigured) {
        userInput.value = item.textContent;
        sendMessage();
      } else {
        apiStatus.textContent = "Please configure your API key first!";
        apiStatus.style.color = "red";
      }
    });
  });
}

// Configure API key
function configureApiKey() {
  const key = apiKeyInput.value.trim();
  if (!key) {
    apiStatus.textContent = "Please enter an API key";
    apiStatus.className = "api-status error";
    apiKeyInput.focus();
    shakeElement(apiKeyInput);
    return;
  }

  // Show loading state
  configureApiBtn.disabled = true;
  configureApiBtn.textContent = "Configuring...";
  apiStatus.textContent = "Connecting to API...";
  apiStatus.className = "api-status";

  // Get checkbox value
  const useCachedData = useCachedDataCheckbox.checked;

  // Send API key and cached data preference to backend
  fetch('/api/configure', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      api_key: key,
      use_cached_data: useCachedData
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      apiKey = key;
      apiStatus.textContent = data.message;
      apiStatus.className = "api-status success";

      // Enable chat interface with animation
      setTimeout(() => {
        userInput.disabled = false;
        sendBtn.disabled = false;
        isApiConfigured = true;

        // Reset button
        configureApiBtn.disabled = false;
        configureApiBtn.textContent = "Configure API Key";

        // Add initial bot message with typing effect
        const welcomeMessage = "I'm your HR Expert Assistant. I can help you with resource planning, competency development, talent acquisition, training programs, and more. How can I assist you today?";
        addMessageWithTypingEffect(welcomeMessage, 'bot');

        // Focus on input
        setTimeout(() => userInput.focus(), 1000);
      }, 500);
    } else {
      apiStatus.textContent = data.message;
      apiStatus.className = "api-status error";
      configureApiBtn.disabled = false;
      configureApiBtn.textContent = "Configure API Key";
      shakeElement(apiKeyInput);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    apiStatus.textContent = "Error configuring API key. Please try again.";
    apiStatus.className = "api-status error";
    configureApiBtn.disabled = false;
    configureApiBtn.textContent = "Configure API Key";
  });
}

// Shake element animation
function shakeElement(element) {
  element.classList.add('shake');
  setTimeout(() => element.classList.remove('shake'), 500);
}

// Add message with typing effect
function addMessageWithTypingEffect(content, type) {
  // First add an empty message
  const messageDiv = document.createElement('div');
  messageDiv.classList.add('message');
  messageDiv.classList.add(type === 'user' ? 'user-message' : 'bot-message');
  messageDiv.innerHTML = '<span class="typing-cursor">|</span>';
  chatMessages.appendChild(messageDiv);

  // Scroll to bottom
  chatMessages.scrollTop = chatMessages.scrollHeight;

  // Then start typing effect
  let i = 0;
  const typingSpeed = 30; // milliseconds per character
  const text = content;
  const typingEffect = setInterval(() => {
    if (i < text.length) {
      // Replace cursor with text typed so far plus cursor
      const textTypedSoFar = text.substring(0, i + 1);
      const formattedText = textTypedSoFar.replace(/\n/g, '<br>');
      messageDiv.innerHTML = formattedText + '<span class="typing-cursor">|</span>';
      i++;
      chatMessages.scrollTop = chatMessages.scrollHeight;
    } else {
      // Typing complete, remove cursor
      const formattedContent = text.replace(/\n/g, '<br>');
      messageDiv.innerHTML = formattedContent;
      clearInterval(typingEffect);

      // Save to chat history
      if (type === 'user') {
        chatHistory.push({ role: 'user', content });
      } else {
        chatHistory.push({ role: 'assistant', content });
      }
    }
  }, typingSpeed);
}

// Add message to chat
function addMessage(content, type) {
  const messageDiv = document.createElement('div');
  messageDiv.classList.add('message');
  messageDiv.classList.add(type === 'user' ? 'user-message' : 'bot-message');

  // Handle line breaks in the content
  const formattedContent = content.replace(/\n/g, '<br>');
  messageDiv.innerHTML = formattedContent;

  // Add animation class
  messageDiv.style.animationDelay = '0.1s';

  chatMessages.appendChild(messageDiv);

  // Scroll to bottom
  chatMessages.scrollTop = chatMessages.scrollHeight;

  // Save to chat history
  if (type === 'user') {
    chatHistory.push({ role: 'user', content });
  } else {
    chatHistory.push({ role: 'assistant', content });
  }

  // Add sound effect (optional)
  if (type === 'user') {
    playSound('send');
  } else {
    playSound('receive');
  }
}

// Play sound effect (optional)
function playSound(type) {
  // This is commented out by default to avoid autoplay restrictions
  // Uncomment to enable sound effects
  /*
  const sound = new Audio();
  if (type === 'send') {
    sound.src = 'static/sounds/send.mp3';
  } else {
    sound.src = 'static/sounds/receive.mp3';
  }
  sound.volume = 0.2;
  sound.play().catch(e => console.log('Sound play prevented by browser'));
  */
}

// Add loading indicator
function addLoadingIndicator() {
  const loadingDiv = document.createElement('div');
  loadingDiv.classList.add('loading');
  loadingDiv.innerHTML = `
    <div class="loading-dots">
      <span></span>
      <span></span>
      <span></span>
    </div>
  `;
  loadingDiv.id = 'loading-indicator';
  chatMessages.appendChild(loadingDiv);
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Remove loading indicator
function removeLoadingIndicator() {
  const loadingDiv = document.getElementById('loading-indicator');
  if (loadingDiv) {
    loadingDiv.remove();
  }
}

// Send message
function sendMessage() {
  const message = userInput.value.trim();
  if (!message || !isApiConfigured) return;

  // Disable input and button while processing
  userInput.disabled = true;
  sendBtn.disabled = true;

  // Add user message to chat
  addMessage(message, 'user');
  userInput.value = '';

  // Add loading indicator with animation
  addLoadingIndicator();

  // Send message to backend
  fetch('/api/chat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      message: message,
      history: conversationMemory
    })
  })
  .then(response => response.json())
  .then(data => {
    // Remove loading indicator
    removeLoadingIndicator();

    if (data.success) {
      // Add bot response with typing effect
      addMessageWithTypingEffect(data.response, 'bot');

      // Save to conversation memory
      saveToMemory(message, data.response);
    } else {
      addMessageWithTypingEffect("Sorry, there was an error processing your request: " + data.message, 'bot');
    }

    // Re-enable input and button
    setTimeout(() => {
      userInput.disabled = false;
      sendBtn.disabled = false;
      userInput.focus();
    }, 300);
  })
  .catch(error => {
    console.error('Error:', error);
    removeLoadingIndicator();
    addMessageWithTypingEffect("Sorry, there was an error processing your request. Please try again.", 'bot');

    // Re-enable input and button
    setTimeout(() => {
      userInput.disabled = false;
      sendBtn.disabled = false;
      userInput.focus();
    }, 300);
  });
}

// Save to conversation memory
function saveToMemory(input, output) {
  conversationMemory.push({ input, output });
}

// Clear chat
function clearChat() {
  // Clear chat interface
  while (chatMessages.firstChild) {
    chatMessages.removeChild(chatMessages.firstChild);
  }

  // Clear chat history and memory
  chatHistory = [];
  conversationMemory = [];

  // Add initial message
  if (isApiConfigured) {
    addMessage("Chat cleared. How can I assist you with HR matters today?", 'bot');
  } else {
    addMessage("Welcome to the HR Expert Assistant! Please configure your API key to start chatting.", 'bot');
  }
}

// Initialize when the page loads
document.addEventListener('DOMContentLoaded', initEventListeners);