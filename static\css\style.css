/* static/css/style.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --secondary-color: #3f37c9;
    --accent-color: #560bad;
    --success-color: #4cc9f0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-dark: #343a40;
    --gray-light: #e9ecef;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    transition: var(--transition);
  }

  body {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    color: var(--dark-color);
    line-height: 1.6;
    padding: 20px;
    min-height: 100vh;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    animation: fadeIn 0.8s ease-in-out;
  }

  header {
    text-align: center;
    margin-bottom: 30px;
    animation: slideDown 0.8s ease-in-out;
  }

  header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 2.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  header p {
    font-size: 1.2rem;
    color: var(--gray-dark);
    font-weight: 300;
    max-width: 800px;
    margin: 0 auto;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideDown {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  /* API Key Section */
  .api-section {
    background-color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 25px;
    transform: translateY(0);
    animation: slideUp 0.6s ease-in-out;
    border-left: 4px solid var(--primary-color);
  }

  .api-section h2 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 1.5rem;
  }

  .api-section p {
    margin-bottom: 15px;
    color: var(--gray-dark);
  }

  .api-input {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
  }

  .api-input input {
    flex-grow: 1;
    padding: 14px;
    border: 2px solid #e1e5eb;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    font-family: 'Roboto', sans-serif;
  }

  .api-input input:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  }

  .api-options {
    margin-bottom: 15px;
  }

  .api-info {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: rgba(67, 97, 238, 0.05);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-light);
  }

  .api-info p {
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--primary-color);
  }

  .api-info ul {
    margin-left: 20px;
    font-size: 14px;
    color: var(--gray-dark);
  }

  .api-info li {
    margin-bottom: 5px;
  }

  /* Checkbox styling */
  .checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 14px;
    user-select: none;
    color: var(--gray-dark);
  }

  .checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #eee;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid #ddd;
  }

  .checkbox-container:hover input ~ .checkmark {
    background-color: #ccc;
  }

  .checkbox-container input:checked ~ .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  .checkbox-container input:checked ~ .checkmark:after {
    display: block;
  }

  .checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  .api-status {
    padding: 12px 15px;
    background-color: var(--gray-light);
    border-radius: var(--border-radius);
    font-size: 14px;
    color: var(--gray-dark);
    transition: var(--transition);
    border-left: 3px solid #6c757d;
  }

  .api-status.success {
    background-color: rgba(76, 201, 240, 0.1);
    border-left: 3px solid var(--success-color);
    color: #0077b6;
  }

  .api-status.error {
    background-color: rgba(255, 107, 107, 0.1);
    border-left: 3px solid #ff6b6b;
    color: #d90429;
  }

  @keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  /* Chat Section */
  .chat-container {
    display: flex;
    flex-direction: column;
    height: 600px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    animation: fadeIn 0.8s ease-in-out 0.2s both;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .chat-messages {
    flex-grow: 1;
    padding: 25px;
    overflow-y: auto;
    background-color: #f8f9fa;
    background-image: url('data:image/svg+xml;charset=utf-8,<svg width="20" height="20" xmlns="http://www.w3.org/2000/svg"><rect width="20" height="20" fill="%23f8f9fa"/><path d="M0 10h20M10 0v20" stroke="%23e9ecef" stroke-width="0.5"/></svg>');
    scroll-behavior: smooth;
  }

  .chat-messages::-webkit-scrollbar {
    width: 8px;
  }

  .chat-messages::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
  }

  .chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }

  .chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }

  .message {
    max-width: 80%;
    margin-bottom: 20px;
    padding: 14px 18px;
    border-radius: 18px;
    line-height: 1.5;
    position: relative;
    word-wrap: break-word;
    animation: messageAppear 0.3s ease-out;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    font-size: 15px;
  }

  .user-message {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 5px;
    text-align: right;
  }

  .bot-message {
    background-color: white;
    color: var(--dark-color);
    margin-right: auto;
    border-bottom-left-radius: 5px;
    border-left: 3px solid var(--primary-light);
  }

  .bot-message::before {
    content: '🧠';
    position: absolute;
    left: -40px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
  }

  .chat-input {
    display: flex;
    padding: 18px;
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    align-items: center;
  }

  .chat-input input {
    flex-grow: 1;
    padding: 14px 18px;
    border: 2px solid #e1e5eb;
    border-radius: 30px;
    font-size: 16px;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    font-family: 'Roboto', sans-serif;
  }

  .chat-input input:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  }

  @keyframes messageAppear {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  button {
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    font-weight: 500;
    letter-spacing: 0.3px;
    box-shadow: 0 4px 6px rgba(67, 97, 238, 0.15);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
  }

  button:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(86, 11, 173, 0.2);
  }

  button:hover::before {
    left: 100%;
  }

  button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(67, 97, 238, 0.15);
  }

  button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
  }

  button:disabled::before {
    display: none;
  }

  #configure-api-btn {
    background-color: var(--accent-color);
  }

  #send-btn {
    margin-left: 10px;
    padding: 12px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  #send-btn::after {
    content: '\2192';
    font-size: 20px;
  }

  .chat-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding: 0 5px;
  }

  #clear-btn {
    background-color: #6c757d;
    font-size: 14px;
    padding: 10px 20px;
  }

  /* Examples Section */
  .examples-section {
    margin-top: 40px;
    animation: fadeIn 0.8s ease-in-out 0.4s both;
  }

  .examples-container {
    background-color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border-top: 4px solid var(--secondary-color);
  }

  .examples-container h3 {
    margin-bottom: 18px;
    color: var(--secondary-color);
    font-weight: 600;
    font-size: 1.3rem;
    position: relative;
    display: inline-block;
  }

  .examples-container h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--secondary-color);
    border-radius: 3px;
  }

  .examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
    margin-bottom: 25px;
  }

  .example-item {
    background-color: #f8f9fa;
    padding: 14px 18px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    font-size: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
  }

  .example-item:hover {
    background-color: rgba(67, 97, 238, 0.05);
    border-left: 3px solid var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  }

  .example-item::before {
    content: '💡';
    position: absolute;
    right: -15px;
    bottom: -15px;
    font-size: 40px;
    opacity: 0.05;
    transition: all 0.3s ease;
  }

  .example-item:hover::before {
    right: -10px;
    bottom: -10px;
    opacity: 0.1;
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
  }

  .loading-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(67, 97, 238, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
  }

  .loading-dots span {
    width: 10px;
    height: 10px;
    margin: 0 4px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: inline-block;
    animation: dot-pulse 1.5s infinite ease-in-out;
  }

  .loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
    background-color: var(--secondary-color);
  }

  .loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
    background-color: var(--accent-color);
  }

  @keyframes dot-pulse {
    0%, 100% { transform: scale(0.7); opacity: 0.7; }
    50% { transform: scale(1); opacity: 1; }
  }

  /* Additional animations */
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }

  /* Fix for background-clip already applied in the header h1 styles */

  /* Typing cursor animation */
  .typing-cursor {
    display: inline-block;
    width: 2px;
    height: 18px;
    background-color: currentColor;
    margin-left: 2px;
    animation: blink 0.7s infinite;
    vertical-align: middle;
  }

  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }

  /* Shake animation for error feedback */
  .shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
  }

  @keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
    40%, 60% { transform: translate3d(3px, 0, 0); }
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .examples-grid {
      grid-template-columns: 1fr;
    }

    .message {
      max-width: 90%;
    }

    .bot-message::before {
      display: none;
    }

    .api-input {
      flex-direction: column;
    }

    .api-input button {
      width: 100%;
      margin-top: 10px;
    }

    header h1 {
      font-size: 2.2rem;
    }
  }

  @media (max-width: 480px) {
    body {
      padding: 10px;
    }

    .container {
      padding: 10px;
    }

    .chat-container {
      height: 500px;
    }

    .chat-input input {
      font-size: 14px;
      padding: 12px 15px;
    }

    #send-btn {
      width: 45px;
      height: 45px;
    }
  }