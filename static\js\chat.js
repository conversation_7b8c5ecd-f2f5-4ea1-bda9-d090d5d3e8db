// Chat page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    const username = sessionStorage.getItem('username');
    if (!username) {
        window.location.href = '/';
        return;
    }

    // DOM Elements
    const usernameDisplay = document.getElementById('username-display');
    const logoutBtn = document.getElementById('logout-btn');
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const clearBtn = document.getElementById('clear-btn');
    const statusIndicator = document.getElementById('status-indicator');
    const exampleBtns = document.querySelectorAll('.example-btn');

    // Variables
    let conversationMemory = [];
    let isProcessing = false;

    // Initialize
    usernameDisplay.textContent = username;
    setupEventListeners();
    autoResizeTextarea();

    function setupEventListeners() {
        // Logout
        logoutBtn.addEventListener('click', logout);

        // Send message
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', handleKeyDown);
        userInput.addEventListener('input', handleInputChange);

        // Clear chat
        clearBtn.addEventListener('click', clearChat);

        // Example questions
        exampleBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const question = btn.getAttribute('data-question');
                userInput.value = question;
                handleInputChange();
                sendMessage();
            });
        });
    }

    function handleKeyDown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    }

    function handleInputChange() {
        autoResizeTextarea();
        sendBtn.disabled = !userInput.value.trim() || isProcessing;
    }

    function autoResizeTextarea() {
        userInput.style.height = 'auto';
        userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
    }

    async function sendMessage() {
        const message = userInput.value.trim();
        if (!message || isProcessing) return;

        // Add user message
        addMessage(message, 'user');
        userInput.value = '';
        handleInputChange();

        // Show typing indicator
        showTypingIndicator();
        setProcessingState(true);

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    history: conversationMemory
                })
            });

            const data = await response.json();
            hideTypingIndicator();

            if (data.success) {
                addMessage(data.response, 'bot');
                saveToMemory(message, data.response);
            } else {
                addMessage('Sorry, I encountered an error. Please try again.', 'bot');
            }
        } catch (error) {
            console.error('Chat error:', error);
            hideTypingIndicator();
            addMessage('Connection error. Please check your internet connection.', 'bot');
        }

        setProcessingState(false);
    }

    function addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = formatMessage(content);

        messageDiv.appendChild(contentDiv);
        
        // Remove welcome message if it exists
        const welcomeMessage = chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }

    function formatMessage(content) {
        // Convert line breaks to HTML
        return content.replace(/\n/g, '<br>');
    }

    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message';
        typingDiv.id = 'typing-indicator';

        const indicatorDiv = document.createElement('div');
        indicatorDiv.className = 'typing-indicator';
        indicatorDiv.innerHTML = `
            <span>AI is thinking</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;

        typingDiv.appendChild(indicatorDiv);
        chatMessages.appendChild(typingDiv);
        scrollToBottom();
    }

    function hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    function setProcessingState(processing) {
        isProcessing = processing;
        sendBtn.disabled = processing || !userInput.value.trim();
        statusIndicator.textContent = processing ? 'Processing...' : 'Ready';
        statusIndicator.style.color = processing ? '#ed8936' : '#48bb78';
    }

    function saveToMemory(input, output) {
        conversationMemory.push({ input, output });
        // Keep only last 10 exchanges to manage memory
        if (conversationMemory.length > 10) {
            conversationMemory = conversationMemory.slice(-10);
        }
    }

    function clearChat() {
        chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">👋</div>
                <h2>Chat Cleared!</h2>
                <p>Feel free to ask me any HR-related questions.</p>
            </div>
        `;
        conversationMemory = [];
    }

    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function logout() {
        sessionStorage.removeItem('username');
        window.location.href = '/';
    }

    // Focus on input
    userInput.focus();
});
