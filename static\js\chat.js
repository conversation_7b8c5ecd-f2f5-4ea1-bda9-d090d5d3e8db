// Chat page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    const username = sessionStorage.getItem('username');
    if (!username) {
        window.location.href = '/';
        return;
    }

    // DOM Elements
    const usernameDisplay = document.getElementById('username-display');
    const logoutBtn = document.getElementById('logout-btn');
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const clearBtn = document.getElementById('clear-btn');
    const statusIndicator = document.getElementById('status-indicator');
    const exampleBtns = document.querySelectorAll('.example-btn');

    // Variables
    let conversationMemory = [];
    let isProcessing = false;

    // Initialize
    usernameDisplay.textContent = username;
    setupEventListeners();
    autoResizeTextarea();

    function setupEventListeners() {
        // Logout
        logoutBtn.addEventListener('click', logout);

        // Send message
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', handleKeyDown);
        userInput.addEventListener('input', handleInputChange);

        // Clear chat
        clearBtn.addEventListener('click', clearChat);

        // Example questions
        exampleBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const question = btn.getAttribute('data-question');
                userInput.value = question;
                handleInputChange();
                sendMessage();
            });
        });
    }

    function handleKeyDown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    }

    function handleInputChange() {
        autoResizeTextarea();
        sendBtn.disabled = !userInput.value.trim() || isProcessing;
    }

    function autoResizeTextarea() {
        userInput.style.height = 'auto';
        userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
    }

    async function sendMessage() {
        const message = userInput.value.trim();
        if (!message || isProcessing) return;

        // Add user message
        addMessage(message, 'user');
        userInput.value = '';
        handleInputChange();

        // Show typing indicator
        showTypingIndicator();
        setProcessingState(true);

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    history: conversationMemory
                })
            });

            const data = await response.json();
            hideTypingIndicator();

            if (data.success) {
                addTypingMessage(data.response, 'bot');
                saveToMemory(message, data.response);
            } else {
                addMessage('Sorry, I encountered an error. Please try again.', 'bot');
            }
        } catch (error) {
            console.error('Chat error:', error);
            hideTypingIndicator();
            addMessage('Connection error. Please check your internet connection.', 'bot');
        }

        setProcessingState(false);
    }

    function addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = formatMessage(content);

        messageDiv.appendChild(contentDiv);
        
        // Remove welcome message if it exists
        const welcomeMessage = chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }

    function formatMessage(content) {
        // Enhanced text formatting with colors and styles
        let formatted = content;

        // Convert markdown-style formatting to HTML
        // Headers
        formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');
        formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
        formatted = formatted.replace(/^# (.*$)/gm, '<h1>$1</h1>');

        // Bold text
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        formatted = formatted.replace(/__(.*?)__/g, '<strong>$1</strong>');

        // Italic text
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
        formatted = formatted.replace(/_(.*?)_/g, '<em>$1</em>');

        // Code blocks
        formatted = formatted.replace(/`(.*?)`/g, '<code>$1</code>');

        // Lists
        formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
        formatted = formatted.replace(/^- (.*$)/gm, '<li>$1</li>');
        formatted = formatted.replace(/^\d+\. (.*$)/gm, '<li>$1</li>');

        // Wrap consecutive list items in ul tags
        formatted = formatted.replace(/(<li>.*<\/li>)/gs, function(match) {
            return '<ul>' + match + '</ul>';
        });

        // Line breaks
        formatted = formatted.replace(/\n/g, '<br>');

        // Clean up multiple consecutive br tags
        formatted = formatted.replace(/(<br>\s*){3,}/g, '<br><br>');

        return formatted;
    }

    function addTypingMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        messageDiv.appendChild(contentDiv);

        // Remove welcome message if it exists
        const welcomeMessage = chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        chatMessages.appendChild(messageDiv);
        scrollToBottom();

        // Start typing animation
        typeText(contentDiv, formatMessage(content));
    }

    function typeText(element, text, speed = 50) {
        element.innerHTML = '';
        element.classList.add('typewriter-text');

        // Split text into words for better typing effect
        const words = text.split(' ');
        let currentWordIndex = 0;
        let currentText = '';

        function typeWord() {
            if (currentWordIndex < words.length) {
                currentText += (currentWordIndex > 0 ? ' ' : '') + words[currentWordIndex];

                // Handle HTML tags properly
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = currentText;
                element.innerHTML = currentText;

                currentWordIndex++;
                setTimeout(typeWord, speed);
                scrollToBottom();
            } else {
                // Typing complete
                element.innerHTML = text;
                element.classList.remove('typewriter-text');
                element.classList.add('typewriter-complete');

                // Apply enhanced formatting
                enhanceMessageFormatting(element);

                // Add a subtle completion effect
                element.style.transform = 'scale(1.01)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 200);
            }
        }

        typeWord();
    }

    // Enhanced message formatting for better visual hierarchy
    function enhanceMessageFormatting(element) {
        // Add special styling for key phrases
        const keyPhrases = [
            'Important:', 'Note:', 'Remember:', 'Key Point:', 'Summary:',
            'Conclusion:', 'Recommendation:', 'Best Practice:', 'Tip:',
            'Warning:', 'Example:', 'Benefits:', 'Advantages:', 'Steps:'
        ];

        keyPhrases.forEach(phrase => {
            const regex = new RegExp(`\\b${phrase}\\b`, 'gi');
            element.innerHTML = element.innerHTML.replace(regex,
                `<span style="color: #e53e3e; font-weight: 600; background: rgba(229, 62, 62, 0.1); padding: 2px 6px; border-radius: 4px;">${phrase}</span>`
            );
        });

        // Highlight numbers and percentages
        element.innerHTML = element.innerHTML.replace(/\b\d+%\b/g,
            '<span style="color: #38a169; font-weight: 600; background: rgba(56, 161, 105, 0.1); padding: 1px 4px; border-radius: 3px;">$&</span>'
        );

        // Highlight years
        element.innerHTML = element.innerHTML.replace(/\b(19|20)\d{2}\b/g,
            '<span style="color: #667eea; font-weight: 500;">$&</span>'
        );
    }

    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message';
        typingDiv.id = 'typing-indicator';

        const indicatorDiv = document.createElement('div');
        indicatorDiv.className = 'typing-indicator';
        indicatorDiv.innerHTML = `
            <span>AI is thinking</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;

        typingDiv.appendChild(indicatorDiv);
        chatMessages.appendChild(typingDiv);
        scrollToBottom();
    }

    function hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    function setProcessingState(processing) {
        isProcessing = processing;
        sendBtn.disabled = processing || !userInput.value.trim();
        statusIndicator.textContent = processing ? 'Processing...' : 'Ready';
        statusIndicator.style.color = processing ? '#ed8936' : '#48bb78';
    }

    function saveToMemory(input, output) {
        conversationMemory.push({ input, output });
        // Keep only last 10 exchanges to manage memory
        if (conversationMemory.length > 10) {
            conversationMemory = conversationMemory.slice(-10);
        }
    }

    function clearChat() {
        chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">👋</div>
                <h2>Chat Cleared!</h2>
                <p>Feel free to ask me any HR-related questions.</p>
            </div>
        `;
        conversationMemory = [];
    }

    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function logout() {
        sessionStorage.removeItem('username');
        window.location.href = '/';
    }

    // Focus on input
    userInput.focus();
});
