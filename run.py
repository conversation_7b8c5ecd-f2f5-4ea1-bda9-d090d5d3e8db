"""
Run script for the HR Expert Assistant application.
This script will start the Flask application.
"""
import os
import sys

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import flask
        import requests
        import bs4
        import langchain
        import langchain_groq
        import groq
        import pypdf
        print("✓ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def main():
    print("=" * 50)
    print("HR Expert Assistant - Starting")
    print("=" * 50)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check if data directory exists
    if not os.path.exists("data"):
        os.makedirs("data")
        print("✓ Created data directory")

    # Check for PDF files
    pdf_files = [f for f in os.listdir("data") if f.lower().endswith('.pdf')]
    if pdf_files:
        print(f"✓ Found {len(pdf_files)} PDF files in data directory")
    else:
        print("ℹ No PDF files found in data directory")

    print("\nStarting Flask application...")
    print("Login page: http://127.0.0.1:5000/")
    print("Chat page: http://127.0.0.1:5000/chat")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)

    try:
        from app import app
        app.run(debug=False, host='127.0.0.1', port=5000)
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
